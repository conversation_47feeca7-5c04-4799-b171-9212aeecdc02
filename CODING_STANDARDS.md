# Coding Standards for Quartermaster Webapp

This document outlines the coding standards and best practices for the project. Adhering to these guidelines ensures code quality, maintainability, and consistency across the codebase.

---

## Table of Contents
1. [General Principles](#general-principles)
2. [File & Folder Structure](#file--folder-structure)
3. [Naming Conventions](#naming-conventions)
4. [JavaScript/JSX Style](#javascriptjsx-style)
5. [React Component Standards](#react-component-standards)
6. [Backend (Node.js/Express) Standards](#backend-nodejsexpress-standards)
7. [Testing Conventions](#testing-conventions)
8. [QA Testing Standards](#qa-testing-standards)
9. [Linting & Formatting](#linting--formatting)
10. [Documentation & Comments](#documentation--comments)
11. [Legacy Code](#legacy-code)
12. [Git & Branching](#git--branching)

---

## General Principles
- **Readability**: Code should be easy to read and understand.
- **Consistency**: Follow established patterns and conventions throughout the codebase.
- **Simplicity**: Prefer simple, clear solutions over complex ones.
- **Reusability**: Write modular, reusable code where possible.
- **Efficiency**: Algorithms and loops should be efficient and require minimal time complexity possible.
- ~~**Testability**: Code should be easy to test and covered by automated tests, having minimum 95% coverage.~~ (this is not applicable at the moment due to rewrite)

---

## File & Folder Structure
- **Frontend** code is located in `frontend/`, with `src/` containing main source files.
  - Components are grouped by feature or domain (e.g., `components/Charts/`, `pages/Dashboard/`).
  - Contexts, hooks, layouts, providers, and utilities are separated into their respective folders.
  - Tests are in `frontend/tests/`, mirroring the source structure.
- **Backend** code is at the root, with clear separation:
  - `models/` for database models
  - `routes/` for API endpoints, with file names ending with route.js
  - `queries/` for reusable complex database queries
  - `services/` for interfacing with the database with given business logic, with file names ending with service.js
  - `middlewares/` for Express middlewares
  - `modules/` for integrations and utilities
  - `utils/` for reusable utility functions
  - `tests/` for backend tests, mirroring the backend structure
- **Scripts** for migrations and utilities are in `scripts/`.

---

## Naming Conventions
- **Files & Folders**:
  - Use `PascalCase` for React components (e.g., `MyComponent.jsx`).
  - Use `camelCase` for utility files and non-component JS files (e.g., `utils.js`).
  - Use `kebab-case` for folders if not a component (e.g., `controllers/`).
- **Variables & Functions**: Use `camelCase`.
- **Classes & Components**: Use `PascalCase`.
- **Constants**: Use `UPPER_CASE_SNAKE_CASE`.
- **Test files**: Use `.test.js` or `.test.jsx` suffix.

---

## JavaScript/JSX Style
- Use **ES6+** features (e.g., `let`, `const`, arrow functions, destructuring).
- Prefer `const` for variables that are not reassigned.
- Use **strict equality** (`===` and `!==`).
- Use **template literals** for string concatenation.
- Avoid deeply nested code; refactor into smaller functions/components.

---

## React Component Standards
- Use **functional components** and React Hooks.
- Name components with `PascalCase`.
- Each component should be in its own file.
- Keep components small and focused; extract logic into hooks or utilities when possible.
- Use destructered props to assist in prop suggestions.
- Use **default props** where appropriate.
- Use scoped styles to avoid global style conflicts, or use theme for global configurations.
- Place assets (images, SVGs) in the `public/` or `public/icons/` directories.
- Use controllers for commuicating with the API. Avoid direct call to the API from a component.

---

## Backend (Node.js/Express) Standards
- Use **ES6+** syntax.
- Organize code by feature/domain (models, routes, services).
- Use **async/await** for asynchronous code; avoid callbacks.
- Handle route errors with centralized error handling middleware.
- Validate all incoming data with the validateData middleware.
- All the routes should be assigned an endpoint id, which is taken from the database collection.
    - This is used to authenticate a route for API keys.
- When creating a new route that reflects a new module in the website, create a new permission for it in the database.
- Use environment variables for configuration/secrets.
    - Make sure to add the environment variables to the .env-example file and inform Mahsam in the PR.
- Keep business logic out of route handlers; use services files.
- Be extra careful when changing public API routes, as they are used by our 3rd-party clients/developers.
    - If you are changing a route, make sure to update the Swagger documentation.
    - Perform the tests in the Postman collection to ensure the route is working as expected, and update with new tests if necessary.
- If you are adding/removing/changing an API endpoint, make sure to update the Postman.

---

## Testing Conventions
- Use **Jest** for unit and integration tests.
- Place tests in the `tests/` directory, mirroring the source structure.
- Name test files with `.test.js` or `.test.jsx` suffix.
- Write tests for all components, utilities, and backend logic.
- Use descriptive test and suite names.
- Mock external dependencies and APIs.

---

## QA Testing Standards

### Development Testing
- **Unit Testing**: Write unit tests for all new functions and components.
- **Integration Testing**: Test API endpoints and database interactions.
- **Component Testing**: Test React components in isolation.
- **Error Handling**: Test error scenarios and edge cases.

### Manual Testing Checklist
Before submitting a PR, developers must complete the following:

#### Frontend Testing
- [ ] **Cross-browser Testing**: Test on Chrome, Firefox, Safari, and Edge
- [ ] **Responsive Design**: Test on desktop, tablet, and mobile devices
- [ ] **User Flow Testing**: Complete end-to-end user journeys
- [ ] **Form Validation**: Test all form inputs and validation messages
- [ ] **Navigation**: Test all navigation paths and breadcrumbs
- [ ] **Data Display**: Verify data is displayed correctly and updates properly
- [ ] **Loading States**: Test loading indicators and error states
- [ ] **Accessibility**: Ensure keyboard navigation and screen reader compatibility

#### Backend Testing
- [ ] **API Endpoint Testing**: Test all new/modified endpoints with Postman
- [ ] **Database Operations**: Verify CRUD operations work correctly
- [ ] **Authentication/Authorization**: Test with different user roles and permissions
- [ ] **Input Validation**: Test with valid, invalid, and edge case inputs
- [ ] **Error Handling**: Verify proper error responses and status codes
- [ ] **Performance**: Test with large datasets and concurrent requests
- [ ] **Security**: Verify no sensitive data is exposed in responses

#### Integration Testing
- [ ] **API Integration**: Test frontend-backend integration
- [ ] **Database Integration**: Verify data consistency across operations
- [ ] **File Uploads**: Test file upload functionality and validation (if applicable)

### Automated Testing
- **Postman Collections**: Maintain updated Postman collections for API testing
- **Regression Testing**: Run full regression tests before major releases
- **Performance Testing**: Monitor API response times and database query performance
- **Security Testing**: Regular security scans and vulnerability assessments

### Testing Environments
- **Development**: For feature development and unit testing
- **Staging**: For integration testing and pre-production validation
- **Production**: For final validation and smoke testing

### Bug Reporting Standards
When reporting bugs, include:
- **Steps to Reproduce**: Clear, step-by-step instructions
- **Expected vs Actual Behavior**: Detailed description of the issue
- **Environment Details**: Browser, OS, device information
- **Screenshots/Logs**: Visual evidence and error logs
- **Priority Level**: Impact assessment (Critical, High, Medium, Low)

### Testing Tools
- **Postman**: API testing and documentation
- **Jest**: Unit and integration testing
- **Browser DevTools**: Frontend debugging and testing
- **Database Tools**: Query testing and data validation
- **Performance Tools**: Load testing and monitoring

---

## Linting & Formatting
- Use **ESLint** for linting (`eslint.config.mjs`, `frontend/eslint.config.js`).
- Use **Prettier** for code formatting (if configured).
- Run linters and formatters before committing code.
- Fix all linter errors and warnings before merging.

---

## Documentation & Comments
- Document complex logic in Confluece where applicable.
- Use **JSDoc** style comments for functions, classes, and complex logic.
- Write clear, concise comments where necessary, but avoid obvious comments.
- Document and keep updated the public APIs in Swagger.

---

## Legacy code
- Note that legacy code builds up in the codebase. This should be refactored from time to time.
- Some existing code may not follow the standards within this document, as they might be new or changed.

---

## Git & Branching
- Create a new branch for every new feature or fix.
- Use Jira ticket id for branch names (e.g., `QMW-1030`).
- Use feature or fix prefix for custom branches (e.g., `fix/what-you-fixed`, `feature/describe-feature`).
- Cut a branch from either dev or staging depending on the nature and urgency of the work.
    - Use the labels in the Jira ticket when cutting a branch
    - Key labels for dev branch: `new-feature`, `refactor`, `optimization`
    - Key labels for staging branch: `quick-feature`, `bug`, `hotfix`
    - Note: If a ticket contains any label from the first group (dev), it must be branched from dev, even if it also includes a label from the second group. For example, a ticket labeled both refactor and bug should be branched from dev, since refactoring is generally riskier and requires a full regression test.
- Write ticket id at start in commit messages (e.g., "QMW-1030: Descriptive message").
- Sync your branch regularly with the parent branch to avoid conflicts.
- Open pull requests for all changes; request review from Mahsam before merging, especially if you are making changes to a core module.
- Keep branches focused; avoid unrelated changes in a single PR.

---

## Additional Notes
- Review and update this document as the project evolves.
- For questions or clarifications, consult the team or the project lead (Mahsam).