const mongoose = require("mongoose");
const ArtifactFlag = require("../models/ArtifactFlag");
const db = require("../modules/db");

class ArtifactFlagService {
    async flagArtifact(artifactId, userId, reason = null) {
        const artifact = await db.qmai.collection("analysis_results").findOne({
            _id: mongoose.Types.ObjectId(artifactId),
        });
        if (!artifact) {
            throw new Error("Artifact not found");
        }

        const existingFlag = await ArtifactFlag.findOne({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId),
        });
        if (existingFlag) {
            throw new Error("You have already flagged this artifact");
        }

        const flag = new ArtifactFlag({
            artifactId: mongoose.Types.ObjectId(artifactId),
            reason: reason || null,
            flaggedBy: mongoose.Types.ObjectId(userId),
        });

        await flag.save();
        return flag;
    }

    async getFlaggedArtifacts() {
        const flaggedArtifacts = await ArtifactFlag.aggregate([
            {
                $lookup: {
                    from: "users",
                    let: { userId: "$flaggedBy" },
                    pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$userId"] } } }, { $project: { _id: 1, name: 1, email: 1 } }],
                    as: "flaggedByUser",
                },
            },
            {
                $group: {
                    _id: "$artifactId",
                    flags: {
                        $push: {
                            _id: "$_id",
                            reason: "$reason",
                            flaggedBy: "$flaggedBy",
                            flaggedByUser: { $arrayElemAt: ["$flaggedByUser", 0] },
                            flaggedAt: "$flaggedAt",
                        },
                    },
                    flagCount: { $sum: 1 },
                    latestFlagDate: { $max: "$flaggedAt" },
                },
            },
            { $sort: { latestFlagDate: -1 } },
        ]);

        const artifactIds = flaggedArtifacts.map((item) => item._id);
        if (artifactIds.length === 0) {
            return [];
        }

        const artifacts = await db.qmai
            .collection("analysis_results")
            .find({ _id: { $in: artifactIds } }, { projection: { _id: 1 } })
            .toArray();

        const results = flaggedArtifacts.map((flagData) => {
            const artifact = artifacts.find((a) => a._id.toString() === flagData._id.toString());
            return {
                artifactId: artifact._id,
                flags: flagData.flags,
                flagCount: flagData.flagCount,
                latestFlagDate: flagData.latestFlagDate,
            };
        });

        return results;
    }

    async unflagArtifact(artifactId, userId) {
        const flag = await ArtifactFlag.findOneAndDelete({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId),
        });
        if (!flag) {
            throw new Error("Flag not found");
        }
        return flag;
    }
}

module.exports = new ArtifactFlagService();