import { useEffect, useState, useRef } from "react";
import { Grid, CircularProgress, Typography } from "@mui/material";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import VirtualizedCardList from "./VirtualizedCardList";
import FlaggedCard from "./FlaggedCard";
import { getSocket } from "../../../socket";
import DetailModal from "./DetailModal";
import theme from "../../../theme";

const FlaggedArtifacts = () => {
    const [flaggedArtifacts, setFlaggedArtifacts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedArtifact, setSelectedArtifact] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const containerRef = useRef();
    const listRef = useRef();

    useEffect(() => {
        const fetchFlaggedArtifacts = async () => {
            try {
                setLoading(true);
                const artifacts = await artifactFlagController.getFlaggedArtifacts();
                setFlaggedArtifacts(artifacts);
            } catch (error) {
                console.error("Error fetching flagged artifacts:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchFlaggedArtifacts();
        const socket = getSocket();

        socket.on("artifacts_flagged/changed", fetchFlaggedArtifacts);

        return () => {
            socket.off("artifacts_flagged/changed", fetchFlaggedArtifacts);
        };
    }, []);

    const handleFlaggedByClick = (artifact) => {
        setSelectedArtifact(artifact);
        setShowDetailModal(true);
    };

    const getRowIndex = (index) => {
        return 380;
    };

    if (loading) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="400px">
                <CircularProgress sx={{ color: "#FFFFFF" }} />
            </Grid>
        );
    }

    if (flaggedArtifacts.length === 0) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="400px">
                <Typography variant="h6" color="#FFFFFF">
                    No flagged artifacts found
                </Typography>
            </Grid>
        );
    }

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"auto"} ref={containerRef}>
                    <VirtualizedCardList
                        ref={listRef}
                        events={flaggedArtifacts}
                        isLoading={loading}
                        containerRef={containerRef}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedArtifact}
                        favouriteArtifacts={[]}
                        CustomCard={FlaggedCard}
                        onFlaggedByClick={handleFlaggedByClick}
                        getRowIndex={getRowIndex}
                    />
                </Grid>
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedArtifact}
                setSelectedCard={setSelectedArtifact}
                favouriteArtifacts={[]}
            />
        </Grid>
    );
};

export default FlaggedArtifacts;
