import { Grid, Typography, Tooltip } from "@mui/material";
import { memo, useMemo, useEffect, useState } from "react";
import dayjs from "dayjs";
import { displayCoordinates, helperIconModes } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useApp } from "../../../hooks/AppHook";
import s3Controller from "../../../controllers/S3.controller.js";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const FlaggedCard = ({ card, onFlaggedByClick }) => {
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const [thumbnail, setThumbnail] = useState(null);
    const { vesselInfo } = useVesselInfo();
    const { timezone } = useApp();

    const roundedCoordinates = useMemo(
        () => displayCoordinates(card.artifact?.location?.coordinates, !!user?.use_MGRS),
        [card.artifact?.location?.coordinates, user?.use_MGRS],
    );

    const vessel = vesselInfo.find((v) => v.vessel_id === card.artifact?.onboard_vessel_id);
    const vesselName = vessel?.name || "Unknown Vessel";
    const isVideo = Boolean(card.artifact?.video_path);

    useEffect(() => {
        if (card.artifact) {
            if (isVideo) {
                setThumbnail(s3Controller.fetchPreviewUrl(card.artifact));
                setSrc(s3Controller.fetchUrl(card.artifact, "video"));
            } else {
                setThumbnail(s3Controller.fetchPreviewUrl(card.artifact));
                setSrc(s3Controller.fetchUrl(card.artifact, "image"));
            }
        }
    }, [card.artifact, isVideo]);

    if (!card.artifact || !vesselInfo) return null;

    return (
        <Grid container paddingTop={"0 !important"} height={"100%"} maxHeight={"350px"} sx={{ cursor: "pointer" }} onClick={() => onFlaggedByClick(card.artifact)}>
            <Grid container backgroundColor={"primary.dark"} borderRadius={2} padding={1} gap={1}>
                <Grid size={12} position="relative">
                    <PreviewMedia
                        thumbnailLink={thumbnail}
                        originalLink={src}
                        cardId={card.artifact._id}
                        isImage={!isVideo}
                        style={{ borderRadius: 8 }}
                        showVideoThumbnail={isVideo}
                        showArchiveButton={!card.artifact.is_archived}
                        showFlagButton={false}
                        isArchived={card.artifact.is_archived}
                        vesselId={card.artifact.onboard_vessel_id}
                        buttonsToShow={[helperIconModes.ARCHIVE]}
                    />
                </Grid>
                <Grid container size={12}>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Tooltip title={vesselName.length > 15 ? vesselName : ""}>
                            <Typography fontSize={"16px"} fontWeight={600} color="#FFFFFF">
                                {vesselName && (vesselName.length > 15 ? vesselName.slice(0, 15) + "..." : vesselName)}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize={"16px"} fontWeight={600} color="#FFFFFF">
                            {dayjs(card.artifact.created_at || card.artifact.timestamp)
                                .tz(timezone)
                                .format("YYYY-MM-DD HH:mm")}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} paddingTop={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} color="#FFFFFF">
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} textAlign={"right"} color="#FFFFFF">
                            {card.artifact.super_category || card.artifact.category || "Unknown"}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} paddingY={2} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} color="#FDBF2D" fontFamily={`"Nunito Sans", sans-serif`} sx={{ fontStyle: "italic", textDecoration: "underline" }}>
                            Flagged by {card.flagCount || 0} {card.flagCount === 1 ? "user" : "users"}
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default memo(FlaggedCard);
