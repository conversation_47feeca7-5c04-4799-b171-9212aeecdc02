import { Modal, <PERSON>, Typography, Button, Grid } from "@mui/material";
import theme from "../theme";

const RemoveFlagConfirmationModal = ({ open, onClose, onConfirm, isLoading }) => {
    const modalStyle = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: { xs: "90%", sm: 500 },
        bgcolor: theme.palette.primary.dark,
        borderRadius: 3,
        boxShadow: 24,
        p: 4,
        outline: "none",
    };

    return (
        <Modal
            open={open}
            onClose={onClose}
            aria-labelledby="remove-flag-modal-title"
            aria-describedby="remove-flag-modal-description"
        >
            <Box sx={modalStyle}>
                <Grid container spacing={3}>
                    <Grid size={12}>
                        <Typography
                            id="remove-flag-modal-title"
                            variant="h4"
                            component="h2"
                            color="#FFFFFF"
                            fontWeight={600}
                            textAlign="center"
                            mb={2}
                        >
                            Remove from Flagged
                        </Typography>
                    </Grid>
                    
                    <Grid size={12}>
                        <Typography
                            id="remove-flag-modal-description"
                            variant="body1"
                            color="#FFFFFF"
                            textAlign="center"
                            fontSize="18px"
                            lineHeight={1.5}
                        >
                            Do you really want to remove this artifact from flagged artifacts.
                        </Typography>
                    </Grid>
                    
                    <Grid container spacing={2} size={12} justifyContent="center" mt={2}>
                        <Grid size={{ xs: 12, sm: 5 }}>
                            <Button
                                fullWidth
                                variant="contained"
                                onClick={onClose}
                                disabled={isLoading}
                                sx={{
                                    backgroundColor: "#FFFFFF",
                                    color: theme.palette.primary.dark,
                                    fontWeight: 600,
                                    fontSize: "16px",
                                    textTransform: "none",
                                    borderRadius: 2,
                                    py: 1.5,
                                    "&:hover": {
                                        backgroundColor: "#F5F5F5",
                                    },
                                    "&:disabled": {
                                        backgroundColor: "#CCCCCC",
                                        color: "#666666",
                                    },
                                }}
                            >
                                Cancel
                            </Button>
                        </Grid>
                        
                        <Grid size={{ xs: 12, sm: 5 }}>
                            <Button
                                fullWidth
                                variant="contained"
                                onClick={onConfirm}
                                disabled={isLoading}
                                sx={{
                                    backgroundColor: theme.palette.custom.mainBlue,
                                    color: "#FFFFFF",
                                    fontWeight: 600,
                                    fontSize: "16px",
                                    textTransform: "none",
                                    borderRadius: 2,
                                    py: 1.5,
                                    "&:hover": {
                                        backgroundColor: "#4A90E2",
                                    },
                                    "&:disabled": {
                                        backgroundColor: "#CCCCCC",
                                        color: "#666666",
                                    },
                                }}
                            >
                                {isLoading ? "Removing..." : "Confirm"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </Box>
        </Modal>
    );
};

export default RemoveFlagConfirmationModal;
