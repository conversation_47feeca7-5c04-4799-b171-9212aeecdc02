import { <PERSON>rid, Modal, Button, Typography } from "@mui/material";
import ModalContainer from "./ModalContainer";

const FlagArtifactModal = ({ open, onClose, onConfirm, isLoading = false, isFlagged = false }) => {
    const handleSubmit = (e) => {
        e.stopPropagation();
        onConfirm();
    };

    const handleClose = (e) => {
        e.stopPropagation();
        onClose();
    };

    return (
        <Modal open={open} onClose={handleClose}>
            <ModalContainer title={isFlagged ? "Remove from Flagged" : "Flag Artifact"} onClose={handleClose} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 400 }}>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Typography fontWeight={300} textAlign={"center"}>
                            {isFlagged ? "Do you really want to remove this artifact from flagged artifacts." : "Are you sure about flagging this artifact?"}
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="outlined"
                                sx={{
                                    color: "#9A9CA2",
                                    "&:disabled": {
                                        background: "#CCCCCC !important",
                                        color: "#666666",
                                    },
                                }}
                                onClick={handleClose}
                                disabled={isLoading}
                            >
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                onClick={handleSubmit}
                                disabled={isLoading}
                                sx={{
                                    "&:disabled": {
                                        background: "#666666 !important",
                                        color: "#CCCCCC",
                                    },
                                }}
                            >
                                {isLoading ? (isFlagged ? "Unflagging..." : "Flagging...") : (isFlagged ? "Confirm" : "Mark as Flagged")}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FlagArtifactModal;
