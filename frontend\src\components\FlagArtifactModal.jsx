import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextField } from "@mui/material";
import ModalContainer from "./ModalContainer";
import theme from "../theme";

const FlagArtifactModal = ({ open, onClose, onConfirm, isLoading = false }) => {
    const [reason, setReason] = useState("");

    const handleSubmit = (e) => {
        e.stopPropagation();
        onConfirm(reason);
        setReason(""); // Reset reason after submit
    };
    
    const handleClose = (e) => {
        e.stopPropagation();
        setReason(""); // Reset reason on close
        onClose();
    };

    return (
        <Modal open={open} onClose={handleClose}>
            <ModalContainer title="Flag Artifact" onClose={handleClose} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 400 }}>
                    <Grid>
                        <TextField
                            fullWidth
                            multiline
                            rows={3}
                            placeholder="Reason (Optional)"
                            value={reason}
                            onChange={(e) => {
                                e.stopPropagation();
                                setReason(e.target.value)
                            }}
                            variant="outlined"
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "#FFFFFF",
                                    "& fieldset": {
                                        borderColor: theme.palette.custom.borderColor,
                                    },
                                    "&:hover fieldset": {
                                        borderColor: theme.palette.primary.main,
                                    },
                                    "&.Mui-focused fieldset": {
                                        borderColor: theme.palette.primary.main,
                                    },
                                },
                                "& .MuiInputBase-input::placeholder": {
                                    color: "#CCCCCC",
                                    opacity: 1,
                                },
                            }}
                        />
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                sx={{ 
                                    background: "#FFFFFF !important", 
                                    color: theme.palette.primary.main,
                                    "&:disabled": {
                                        background: "#CCCCCC !important",
                                        color: "#666666",
                                    }
                                }}
                                onClick={handleClose}
                                disabled={isLoading}
                            >
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button 
                                variant="contained" 
                                onClick={handleSubmit}
                                disabled={isLoading}
                                sx={{
                                    "&:disabled": {
                                        background: "#666666 !important",
                                        color: "#CCCCCC",
                                    }
                                }}
                            >
                                {isLoading ? "Flagging..." : "Mark as Flagged"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FlagArtifactModal;
