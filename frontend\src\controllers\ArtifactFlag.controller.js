import axiosInstance from "../axios";

class ArtifactFlagController {
    constructor() {
        this.flaggedArtifactsMap = new Map();
    }

    async flagArtifact(artifactId, reason = null) {
        try {
            const payload = {}
            if (reason) {
                payload.reason = reason.trim();
            }
            const response = await axiosInstance.post(`/artifacts/${artifactId}/flag`, payload);
            return response.data;
        } catch (error) {
            console.error("Error flagging artifact:", error);
            throw error;
        }
    }

    async getFlaggedArtifacts() {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged`);
            const artifacts = response.data || [];
            this.flaggedArtifactsMap = new Map();
            for (const flagged of artifacts) {
                const key = flagged.artifactId?.toString();
                if (key) {
                    this.flaggedArtifactsMap.set(key, flagged.flags || []);
                }
            }
            return artifacts;
        } catch (error) {
            console.error("Error fetching flagged artifacts:", error);
            throw error;
        }
    }

    async unflagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/unflag`);
            return response.data;
        } catch (error) {
            console.error("Error unflagging artifact:", error);
            throw error;
        }
    }

    isArtifactFlaggedByUser(artifactId, userId) {
        const key = artifactId?.toString();
        const flags = this.flaggedArtifactsMap.get(key) || [];
        return flags.some(flag => flag.flaggedBy?.toString() === userId?.toString());
    }
}

const artifactFlagController = new ArtifactFlagController();
export default artifactFlagController;