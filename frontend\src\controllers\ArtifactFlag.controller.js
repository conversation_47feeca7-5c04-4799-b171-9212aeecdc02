import axiosInstance from "../axios";

class ArtifactFlagController {
    constructor() {
        this.flaggedArtifactsMap = new Map();
    }

    async flagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/flag`, { meta: { showSnackbar: true } });
            return response.data;
        } catch (error) {
            console.error("Error flagging artifact:", error);
            throw error;
        }
    }

    async getFlaggedArtifacts() {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged`, { meta: { showSnackbar: true } });
            const artifacts = response.data || [];
            this.flaggedArtifactsMap = new Map();
            for (const flagged of artifacts) {
                const key = flagged.artifactId;
                if (key) {
                    this.flaggedArtifactsMap.set(key, flagged.flags || []);
                }
            }
            return artifacts;
        } catch (error) {
            console.error("Error fetching flagged artifacts:", error);
            throw error;
        }
    }

    async unflagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/unflag`, { meta: { showSnackbar: true } });
            return response.data;
        } catch (error) {
            console.error("Error unflagging artifact:", error);
            throw error;
        }
    }

    isArtifactFlaggedByUser(artifactId, userId) {
        const key = artifactId;
        const flags = this.flaggedArtifactsMap.get(key) || [];
        return flags.some((flag) => flag.flaggedBy === userId);
    }
}

const artifactFlagController = new ArtifactFlagController();
export default artifactFlagController;
