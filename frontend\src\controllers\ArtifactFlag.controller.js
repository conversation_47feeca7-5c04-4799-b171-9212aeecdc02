import axiosInstance from "../axios";

class ArtifactFlagController {
    constructor() {
        this.userFlaggedArtifactIds = new Set();
    }

    async flagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/flag`, { meta: { showSnackbar: true } });
            this.userFlaggedArtifactIds.add(artifactId);
            return response.data;
        } catch (error) {
            console.error("Error flagging artifact:", error);
            throw error;
        }
    }

    async getFlaggedArtifacts() {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged`, { meta: { showSnackbar: true } });
            const artifacts = response.data || [];
            return artifacts;
        } catch (error) {
            console.error("Error fetching flagged artifacts:", error);
            throw error;
        }
    }

    async unflagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/unflag`, { meta: { showSnackbar: true } });
            this.userFlaggedArtifactIds.delete(artifactId);
            return response.data;
        } catch (error) {
            console.error("Error unflagging artifact:", error);
            throw error;
        }
    }

    isArtifactFlaggedByUser(artifactId) {
        return this.userFlaggedArtifactIds.has(artifactId);
    }

    async getUserFlaggedArtifactIds() {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged/user`);
            const flaggedIds = response.data.flaggedArtifactIds || [];
            this.userFlaggedArtifactIds = new Set(flaggedIds);
            return flaggedIds;
        } catch (error) {
            console.error("Error fetching user flagged artifact IDs:", error);
            throw error;
        }
    }
}

const artifactFlagController = new ArtifactFlagController();
export default artifactFlagController;
