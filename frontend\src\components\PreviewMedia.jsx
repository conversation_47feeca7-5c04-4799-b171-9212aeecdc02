import React, { useEffect, useState } from "react";
import { Box, CircularProgress, Modal, IconButton, Skeleton, Tooltip } from "@mui/material";
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import ShareModal from "./ShareModal";
import VideoPlayer from "../components/VideoPlayer";
import { useUser } from "../hooks/UserHook";
import favouriteArtifactsController from "../controllers/FavouriteArtifacts.controller";
import artifactFlagController from "../controllers/ArtifactFlag.controller";
import axiosInstance from "../axios.js";
import { procDownloadResponse } from "../utils.js";
import FullscreenMediaModal from "./FullscreenMediaModal";
import HelperIcons from "./HealperIcons.jsx";
import DetailVideoPlayer from "./DetailVideoPlayer.jsx";
import ArchiveConfirmModal from "./ArchiveConfirmModal";
import FlagArtifactModal from "./FlagArtifactModal";
import idb from "../indexedDB.js";
import FlagIcon from "@mui/icons-material/Flag";
import OutlinedFlagIcon from "@mui/icons-material/OutlinedFlag";
import { helperIconModes } from '../utils';

const PreviewMedia = ({
    thumbnailLink,
    originalLink,
    isImage,
    showVideoModal = false,
    style = {},
    skeletonStyle = { borderRadius: "8px", height: 200 },
    cardId,
    favouriteArtifacts,
    showFullscreenIcon = false,
    showVideoThumbnail = false,
    showArchiveButton = false,
    showFlagButton = true,
    showFullscreenIconForMap,
    // userTest,
    onThumbnailClick,
    isArchived = false,
    vesselId,
    direction,
    buttonsToShow,
}) => {
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isFavouriteLoading, setIsFavouriteLoading] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [fullscreenOpen, setFullscreenOpen] = useState(false);
    const [isFavourite, setIsFavourite] = useState(() => favouriteArtifacts?.some((artifact) => artifact.artifact_id === cardId));
    const mediaRef = React.useRef(null);
    const { user } = useUser();
    const [currentTime, setCurrentTime] = useState(0);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [archiveAction, setArchiveAction] = useState(null); // 'archive' or 'unarchive'
    const [isArchiving, setIsArchiving] = useState(false);
    const [archived, setArchived] = useState(false);
    const [flagModalOpen, setFlagModalOpen] = useState(false);
    const [isFlagged, setIsFlagged] = useState(false);
    const [isFlagging, setIsFlagging] = useState(false);

    useEffect(() => {
        setIsFavourite(favouriteArtifacts?.some((artifact) => artifact.artifact_id === cardId));
        setIsFavouriteLoading(false);
    }, [cardId, favouriteArtifacts]);

    useEffect(() => {
        setArchived(isArchived);
    }, [isArchived]);

    useEffect(() => {
        const mediaElement = mediaRef.current;

        if (!mediaElement) return;

        return () => {
            if (isImage) {
                console.log("Cancel image");
                mediaElement.src = ""; // Or a 1x1 transparent GIF: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            } else {
                console.log("Cancel video");
                mediaElement.pause();
                mediaElement.removeAttribute("src");
                // mediaElement.src = ''; // Alternative
                mediaElement.load();
            }
        };
    }, [cardId]);

    const toggleShare = (e) => {
        e.stopPropagation();
        setIsShareModalOpen((prev) => !prev);
    };

    const handleCurrentTimeChange = (time = 0) => {
        setCurrentTime(time);
    };

    const handleFullscreenOpen = (e) => {
        e.stopPropagation();
        setFullscreenOpen(true);
    };

    const handleFullscreenClose = () => {
        setFullscreenOpen(false);
    };

    const handleThumbnailClick = (e) => {
        e.stopPropagation();
        if (onThumbnailClick) {
            onThumbnailClick(e);
        } else {
            handleFullscreenOpen(e);
        }
    };

    const downloadArtifact = async () => {
        setIsDownloading(true);
        await axiosInstance
            .post(
                `artifacts/${cardId}/download`,
                {},
                {
                    responseType: "blob",
                    timeout: 120000,
                },
            )
            .then(procDownloadResponse)
            .catch((e) => {
                console.error(e);
            })
            .finally(() => {
                setIsDownloading(false);
            });
    };

    const addFavourite = async () => {
        if (!cardId && !user._id) return;
        setIsFavouriteLoading(true);
        const data = {
            // user_id: user && user._id ? user._id : userTest._id,
            artifact_id: cardId,
        };
        const response = await favouriteArtifactsController.addFavouriteArtifact(data);
        if (response.status === 201) {
            setIsFavourite(true);
        }
        setIsFavouriteLoading(false);
    };
    const removeFavourite = async () => {
        if (!cardId && !user._id) return;
        setIsFavouriteLoading(true);
        const data = {
            // user_id: user && user._id ? user._id : userTest._id,
            artifact_id: cardId,
        };
        const response = await favouriteArtifactsController.removeFavouriteArtifact(data);
        if (response.status === 200) {
            setIsFavourite(false);
        }
        setIsFavouriteLoading(false);
    };

    const handleArchiveClick = (e) => {
        e.stopPropagation();
        if (archived) {
            setArchiveAction("unarchive");
            setConfirmOpen(true);
        } else {
            handleConfirm(e, true);
        }
    };

    const handleConfirm = async (e, skipConfirmClose = false) => {
        e.stopPropagation();
        if (!skipConfirmClose) setConfirmOpen(false);
        setIsArchiving(true);
        try {
            let updatedArtifact = null;
            if (archiveAction === "archive" || skipConfirmClose) {
                const res = await axiosInstance.post(`/artifacts/${cardId}/archive`);
                updatedArtifact = res.data?.artifact;
                window.dispatchEvent(new CustomEvent("clusterinfowindow/artifactArchived", { detail: { artifactId: cardId } }));
            } else {
                const res = await axiosInstance.post(`/artifacts/${cardId}/unarchive`);
                updatedArtifact = res.data?.artifact;
            }
            if (vesselId) {
                await idb.deleteItem(vesselId + "_artifact", cardId).catch((err) => {
                    console.error("Error deleting artifact from indexedDB", err);
                });
            }
            window.dispatchEvent(new CustomEvent("artifact/changed", { detail: { artifact: updatedArtifact } }));
        } catch (err) {
            console.error(err);
        } finally {
            setIsArchiving(false);
        }
    };

    const handleFlagClick = (e) => {
        e.stopPropagation();
        setFlagModalOpen(true);
    };

    const handleFlagConfirm = async () => {
        setIsFlagging(true);
        try {
            if (isFlagged) {
                await artifactFlagController.unflagArtifact(cardId);
                setIsFlagged(false);
                setFlagModalOpen(false);
                return;
            }
            await artifactFlagController.flagArtifact(cardId);
            setIsFlagged(true);
            setFlagModalOpen(false);
        } catch (error) {
            console.error("Error flagging artifact:", error);
        } finally {
            setIsFlagging(false);
        }
    };

    const handleFlagModalClose = () => {
        setFlagModalOpen(false);
    };

    useEffect(() => {
        if (!isImage && showVideoModal) {
            setIsLoading(false);
        }
    }, [showVideoModal]);

    useEffect(() => {
        const loadFlaggedArtifacts = async () => {
            if (cardId && user?._id) {
                try {
                    const flagged = artifactFlagController.isArtifactFlaggedByUser(cardId, user._id);
                    setIsFlagged(flagged);
                } catch (error) {
                    console.error("Error loading flagged artifacts:", error);
                }
            }
        };

        loadFlaggedArtifacts();
    }, [cardId, user]);

    return (
        <Box
            position="relative"
            sx={{
                width: "100%",
                height: "100%",
            }}
        >
            {isLoading && (isImage || !showVideoModal) && (
                <>
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            position: "absolute",
                            top: 0,
                            left: 0,
                            width: "100%",
                            height: "100%",
                            zIndex: 1,
                        }}
                    >
                        <CircularProgress />
                    </Box>
                    <Skeleton variant="rectangular" width="100%" sx={{ ...skeletonStyle }} />
                </>
            )}

            {isImage ? (
                <>
                    <img
                        src={thumbnailLink}
                        ref={mediaRef}
                        alt="media"
                        style={{ width: "100%", height: "100%", objectFit: "cover", display: isLoading ? "none" : "block", ...style }}
                        onLoad={() => setIsLoading(false)}
                    />
                    {!isLoading && showFullscreenIcon && !showFullscreenIconForMap && (
                        <Tooltip title="View Full Screen" arrow placement="right">
                            <IconButton
                                onClick={handleFullscreenOpen}
                                sx={{
                                    position: "absolute",
                                    bottom: 8,
                                    right: 8,
                                    height: 27,
                                    width: 27,
                                    padding: 0,
                                    color: "#fff",
                                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                                    borderRadius: "50%",
                                    "&:hover": {
                                        backgroundColor: "rgba(0, 0, 0, 0.7)",
                                    },
                                }}
                            >
                                <FullscreenIcon sx={{ height: 18 }} />
                            </IconButton>
                        </Tooltip>
                    )}
                </>
            ) : showVideoModal ? (
                <>
                    <VideoPlayer ref={mediaRef} src={originalLink} onFullscreen={() => setIsModalOpen(true)} />
                    <Modal open={isModalOpen} onClose={() => setIsModalOpen(false)}>
                        <Box sx={{ width: "100%", height: "100%", backgroundColor: "#000" }}>
                            <VideoPlayer src={originalLink} isFullscreen onFullscreen={() => setIsModalOpen(false)} />
                        </Box>
                    </Modal>
                </>
            ) : showVideoThumbnail ? (
                <>
                    <img
                        src={thumbnailLink}
                        alt="media"
                        style={{ width: "100%", height: "100%", objectFit: "cover", display: isLoading ? "none" : "block", ...style }}
                        onLoad={() => setIsLoading(false)}
                        onClick={handleThumbnailClick}
                    />
                    {!isLoading && (
                        <Box
                            sx={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: "rgba(0, 0, 0, 0.3)",
                                borderRadius: "8px",
                                cursor: "pointer",
                            }}
                            onClick={handleThumbnailClick}
                        >
                            <PlayCircleOutlineIcon
                                sx={{
                                    fontSize: 50,
                                    color: "white",
                                    filter: "drop-shadow(0px 2px 3px rgba(0,0,0,0.5))",
                                }}
                            />
                        </Box>
                    )}
                </>
            ) : (
                <>
                    <DetailVideoPlayer
                        src={originalLink}
                        style={{ width: "100%", height: "100%", display: isLoading ? "none" : "block", objectFit: "cover", ...style }}
                        onLoadedData={() => setIsLoading(false)}
                        onCurrentTimeChange={handleCurrentTimeChange}
                        currentTime={currentTime}
                        fullscreenOpen={fullscreenOpen}
                        isInFullScreen={false}
                        showFullscreenIcon={true}
                        setFullscreenOpen={setFullscreenOpen}
                    />
                    {/* <video
                        src={originalLink}
                        preload="auto"
                        controls
                        style={{ width: "100%", height: "100%", display: isLoading ? "none" : "block", objectFit: "cover", ...style }}
                        onLoadedData={() => setIsLoading(false)}
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    /> */}
                </>
            )}

            {!isLoading && !isArchived && showFlagButton && handleFlagClick && (
                <Tooltip title={!isFlagging ? (isFlagged ? "Flagged" : "Flag Artifact") : null} arrow placement="right">
                    <IconButton
                        size="small"
                        sx={{
                            position: "absolute",
                            top: 8,
                            left: 8,
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: isFlagged ? "#E60000CC": "rgba(0,0,0,0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0,0,0,0.7)",
                            },
                        }}
                        onClick={handleFlagClick}
                        disabled={isFlagging}
                    >
                        {isFlagging ? (
                            <CircularProgress sx={{ color: "white" }} size={18} />
                        ) : isFlagged ? (
                            <FlagIcon sx={{ height: 18 }} />
                        ) : (
                            <OutlinedFlagIcon sx={{ height: 18 }} />
                        )}
                    </IconButton>
                </Tooltip>
            )}

            {!isLoading && (
                <HelperIcons
                    buttonsToShow={buttonsToShow || [
                        helperIconModes.FAVOURITE,
                        helperIconModes.SHARE,
                        helperIconModes.DOWNLOAD,
                        ...(showArchiveButton ? [helperIconModes.ARCHIVE] : []),
                        ...(showFullscreenIconForMap ? [helperIconModes.FULLSCREEN] : []),
                    ]}
                    buttonHandlers={{
                        addFavourite,
                        removeFavourite,
                        toggleShare,
                        downloadArtifact,
                        handleFullscreenOpen,
                        handleArchiveClick,
                    }}
                    buttonStates={{
                        isFavourite,
                        isFavouriteLoading,
                        isDownloading,
                        showFullscreenIconForMap,
                        archived,
                        isArchiving,
                    }}
                    direction={direction}
                />
            )}
            <ShareModal state={isShareModalOpen} onClose={toggleShare} shareLink={originalLink || thumbnailLink} isImageLink={isImage} />
            <ArchiveConfirmModal
                initialState={confirmOpen}
                onClose={(e) => {
                    e.stopPropagation();
                    setConfirmOpen(false);
                }}
                onConfirm={(e) => handleConfirm(e)}
                isArchive={archiveAction === "archive"}
            />
            <FlagArtifactModal open={flagModalOpen} onClose={handleFlagModalClose} onConfirm={handleFlagConfirm} isLoading={isFlagging} isFlagged={isFlagged} />
            {fullscreenOpen && (
                <FullscreenMediaModal
                    isLoading={isLoading}
                    isFavourite={isFavourite}
                    removeFavourite={removeFavourite}
                    addFavourite={addFavourite}
                    toggleShare={toggleShare}
                    downloadArtifact={downloadArtifact}
                    open={fullscreenOpen}
                    onClose={handleFullscreenClose}
                    mediaUrl={originalLink}
                    isImage={isImage}
                    handleCurrentTimeChange={handleCurrentTimeChange}
                    currentTime={currentTime}
                    showFullscreenIcon={true}
                    setFullscreenOpen={setFullscreenOpen}
                />
            )}
        </Box>
    );
};

export default PreviewMedia;
