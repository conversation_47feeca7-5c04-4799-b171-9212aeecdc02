import { Grid } from "@mui/material";
import { useEffect, useState, memo, useRef } from "react";
import DetailModal from "./DetailModal";
import { useParams } from "react-router-dom";
import theme from "../../../theme";
import VirtualizedCardList from "./VirtualizedCardList";
import axiosInstance from "../../../axios";

const ArchivedArtifacts = ({ vessels }) => {
    const { id } = useParams();
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const virtualizedListContainerRef = useRef();

    const fetchArtifacts = async () => {
        setIsLoading(true);
        try {
            const res = await axiosInstance.get("/artifacts/archived");
            setEvents(res.data.artifacts || []);
            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
            console.error("Error fetching archived artifacts", err);
        }
    };

    useEffect(() => {
        const unidIds = vessels.map((v) => v.unit_id);
        setFilteredEvents(events.filter((e) => unidIds.includes(e.unit_id)));
    }, [events, vessels]);

    useEffect(() => {
        fetchArtifacts();
    }, [id]);

    useEffect(() => {
        const refetch = () => fetchArtifacts();
        window.addEventListener("artifact/changed", refetch);
        return () => window.removeEventListener("artifact/changed", refetch);
    }, []);

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"auto"} ref={virtualizedListContainerRef}>
                    <VirtualizedCardList
                        events={filteredEvents}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedCard}
                        favouriteArtifacts={[]}
                        isLoading={isLoading}
                        containerRef={virtualizedListContainerRef}
                    />
                </Grid>
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                favouriteArtifacts={[]}
            />
        </Grid>
    );
};

export default memo(ArchivedArtifacts);
