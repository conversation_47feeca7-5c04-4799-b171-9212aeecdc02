import { Grid, Modal, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import s3Controller from "../../../controllers/S3.controller";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";

export default function DetailModal({ showDetailModal, setShowDetailModal, selectedCard, setSelectedCard, favouriteArtifacts, id }) {
    const { screenSize, timezone } = useApp();
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    // const [locationName, setLocationName] = useState('Loading...');
    // const location = selectedCard?.location ? { lat: selectedCard.location.coordinates[1], lng: selectedCard.location.coordinates[0] } : null;
    const key = selectedCard?.location?.coordinates && displayCoordinates(selectedCard.location.coordinates, !!user?.use_MGRS);

    // const fetchGeolocation = async () => {
    //     try {
    //         const name = await getLocation(location);
    //         setLocationName(name);
    //     } catch (err) {
    //         console.error('Error fetching geolocation:', err);
    //     }
    // };

    const handleClose = () => {
        setSelectedCard(null);
        setShowDetailModal(false);
    };

    const details = [
        { label: "Location", value: key },
        { label: "Category", value: selectedCard?.super_category || "Unspecified category" },
        { label: "Sub Category", value: selectedCard?.category },
        { label: "Weapons", value: selectedCard?.weapons },
        { label: "Size", value: selectedCard?.size },
        { label: "Color", value: selectedCard?.color },
        { label: "Imo Number", value: selectedCard?.imo_number },
        { label: "Country Flag", value: selectedCard?.country_flag },
        {
            label: "Text Detected",
            value:
                Array.isArray(selectedCard?.text_extraction) && selectedCard.text_extraction.length > 0
                    ? selectedCard.text_extraction
                          .map((e) => e.text)
                          .slice(0, 5)
                          .join(", ")
                    : null,
        },
        { label: "Description", value: selectedCard?.others },
    ];

    const fieldWithFullWidth = ["Text Detected", "Description"];

    useEffect(() => {
        if (selectedCard) {
            setSrc(s3Controller.fetchUrl(selectedCard, selectedCard.video_path ? "video" : "image"));
        }
    }, [selectedCard]);

    // useEffect(() => {
    //     if (location) {
    //         fetchGeolocation();
    //     }
    // }, [location]);

    return (
        <Modal open={Boolean(showDetailModal)} onClose={handleClose}>
            <ModalContainer title="Event Details" onClose={handleClose} showDivider>
                <Grid container gap={1} maxHeight="70vh" overflow="auto" minWidth={{ xs: 300, sm: 500 }}>
                    <Grid size={12}>
                        {
                            selectedCard && (
                                <PreviewMedia
                                    thumbnailLink={src}
                                    originalLink={src}
                                    cardId={id || selectedCard._id}
                                    isImage={!selectedCard.video_path}
                                    style={{ borderRadius: 8, height: 300, objectFit: "contain", backgroundColor: "#000" }}
                                    favouriteArtifacts={favouriteArtifacts}
                                    skeletonStyle={{ height: 300, width: "100%" }}
                                    showFullscreenIconForMap={!selectedCard.video_path}
                                    showArchiveButton={hasManageArtifacts}
                                />
                            )
                            // :
                            // selectedCard?.video_path ? (
                            //     <video
                            //         src={`${environment.VITE_API_URL}/api/artifacts/link/${selectedCard?._id}`}
                            //         width={"100%"}
                            //         controls
                            //         style={{ borderRadius: "8px" }}
                            //     ></video>
                            // ) : (
                            //     <img
                            //         src={`${environment.VITE_API_URL}/api/artifacts/link/${selectedCard?._id}`}
                            //         width="100%"
                            //         loading="lazy"
                            //         style={{ borderRadius: "8px" }}
                            //         alt="Event"
                            //     />
                            // )
                        }
                    </Grid>

                    <Grid
                        display="flex"
                        justifyContent={screenSize.xs ? "flex-start" : "space-between"}
                        alignItems={screenSize.xs ? "flex-start" : "center"}
                        paddingX={1}
                        flexDirection={screenSize.xs ? "column" : "row"}
                        size={12}
                    >
                        {screenSize.xs && (
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                Name
                            </Typography>
                        )}
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {selectedCard?.vesselName}
                        </Typography>
                        {screenSize.xs && (
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                Timestamp
                            </Typography>
                        )}
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {dayjs(selectedCard?.timestamp)
                                .tz(timezone)
                                .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>

                    {details.map(({ label, value }, index) => (
                        <React.Fragment key={index}>
                            <Grid
                                display="flex"
                                alignItems={{
                                    xs: "flex-start",
                                    sm: index % 2 == 0 || fieldWithFullWidth.includes(label) ? "flex-start" : "flex-end",
                                }}
                                paddingX={1}
                                flexDirection={"column"}
                                size={{
                                    xs: 12,
                                    sm: fieldWithFullWidth.includes(label) ? 12 : 5.9,
                                }}
                            >
                                <Typography fontSize="16px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                                    {label}
                                </Typography>
                                <Typography fontSize="16px" fontWeight={500}>
                                    {value ?? "--"}
                                </Typography>
                            </Grid>
                        </React.Fragment>
                    ))}
                </Grid>
            </ModalContainer>
        </Modal>
    );
}
